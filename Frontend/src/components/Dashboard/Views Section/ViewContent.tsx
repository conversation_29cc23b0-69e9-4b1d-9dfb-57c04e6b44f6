import React, { useRef } from 'react';
import { FileData, ComponentType, ColumnSelection, DateFilter } from './types';
import { PanelFilter } from './FilterTypes';
import GridLayout from './GridLayout';
import { Button, message } from 'antd';
import { SaveOutlined, ClearOutlined } from '@ant-design/icons';
import { putRequest } from '../../../utils/apiHandler';
import Notiflix from 'notiflix';
import TabsInterface from './Tabs/TabsInterface';
import AppliedFilters from './AppliedFilters';
import './ViewStyles.css';

interface ViewContentProps {
  selectedFile: FileData | null;
  filteredData?: any; // Pre-filtered data (for non-exploration modes)
  panelSpecificData?: Record<ComponentType, any>; // Panel-specific data for exploration mode
  panelLoadingStates?: Record<ComponentType, boolean>; // Loading states for exploration mode
  panelErrorStates?: Record<ComponentType, string | null>; // Error states for exploration mode
  isExplorationMode?: boolean; // Flag to determine data source
  createInitialPanels?: boolean;
  activePanels?: ComponentType[];
  onPanelsChange?: (activePanels: ComponentType[]) => void;

  // Selection and filter props
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;
  panelFilters?: Record<string, PanelFilter[]>;
  conditionalFilters?: PanelFilter[];

  // Selection and filter handlers

  onColumnSelection?: (indices: number[], headers: string[]) => void;
  onDateFilterChange?: (startDate: string | null, endDate: string | null) => void;
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string , data?:any) => void;
  onClearAllFilters?: () => void;

  // Filter management handlers
  onAddFilter?: (filter: PanelFilter, panelId: string) => void;
  onRemoveFilter?: (filterId: string, panelId: string) => void;

  // View structure from database
  structure?: any;
}

const ViewContent: React.FC<ViewContentProps> = ({
  selectedFile,
  filteredData,
  panelSpecificData,
  panelLoadingStates,
  panelErrorStates,
  isExplorationMode = false,
  createInitialPanels = false,
  onPanelsChange,
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  panelFilters = {},
  conditionalFilters = [],
  onColumnSelection,
  onDateFilterChange,
  onZoomSelection,
  onClearAllFilters,
  onAddFilter,
  onRemoveFilter,
  structure
}) => {
  // Reference to the GridLayout component
  const gridLayoutRef = useRef<{getLayout: () => any[], getItems: () => any[]}>(null);

  // Helper function to map component types to panel type IDs
  const getPanelTypeId = (componentType: ComponentType): number => {
    switch (componentType) {
      case ComponentType.TimeSeriesPanel:
        return 1;
      case ComponentType.OverviewPanel:
        return 2;
      case ComponentType.HistogramPanel:
        return 3;
      case ComponentType.DataTablePanel:
        return 4;
      default:
        return 0;
    }
  };

  // Note: View saving is now handled individually by panels
  // Each panel saves itself when modified, so no bulk view saving is needed



  return (
    <>
    <TabsInterface />
    <div className="view-content flex-1 p-4 h-full relative">
      {selectedFile ? (
        <div className="h-full">
          {/* Applied Filters Section */}
          <div className="flex flex-wrap items-center mb-4">
            <div className="flex-grow">
              <AppliedFilters
                selectedColumns={selectedColumns}
                dateFilter={dateFilter}
                conditionalFilters={conditionalFilters}
                onRemoveFilter={onRemoveFilter || (() => {})}
                onClearAllFilters={onClearAllFilters || (() => {})}
              />
            </div>

            {/* Buttons */}
            <div className="flex gap-2">
              <Button
                type="default"
                icon={<ClearOutlined />}
                onClick={() => {
                  if (onClearAllFilters) {
                    onClearAllFilters();
                  } else {
                    // Fallback to legacy approach
                    if (onColumnSelection) onColumnSelection([], []);
                    if (onDateFilterChange) onDateFilterChange(null, null);
                  }
                }}
                style={{ boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)' }}
              >
                Clear Filters
              </Button>
              {/* <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSaveView}
                style={{ boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)' }}
              >
                Save View
              </Button> */}
            </div>
          </div>

          <GridLayout
            ref={gridLayoutRef}
            selectedFile={selectedFile}
            filteredData={filteredData}
            panelSpecificData={panelSpecificData}
            panelLoadingStates={panelLoadingStates}
            panelErrorStates={panelErrorStates}
            isExplorationMode={isExplorationMode}
            createInitialPanels={createInitialPanels}
            onPanelsChange={onPanelsChange}
            selectedColumns={selectedColumns}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            conditionalFilters={conditionalFilters}
            onColumnSelection={onColumnSelection}
            onDateFilterChange={onDateFilterChange}
            onZoomSelection={onZoomSelection}
            onAddFilter={onAddFilter}
            onRemoveFilter={onRemoveFilter}
            structure={structure}
          />
        </div>
      ) : (
        <div className="flex items-center justify-center h-full">
          <p className="text-gray-500">Select a file from the sidebar to view content</p>
        </div>
      )}
    </div>
    </>
  );
};

export default ViewContent;
