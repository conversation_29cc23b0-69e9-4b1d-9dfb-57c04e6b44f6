import { useState } from 'react';
import { postRequest } from '../utils/apiHandler';
import { ComponentType } from '../components/Dashboard/Views Section/types';

export interface FilterPayload {
  fileId: string;
  fileName: string;
  selectedColumns: {
    indices: number[];
    headers: string[];
  };
  dateFilter: {
    startDate: string | null;
    endDate: string | null;
  };
  conditionalFilters: any[];
  annotationFilters: any[];
  operationFilters: any[];
  explorationType: string;
  timestamp: string;
}

export interface PanelDataResponse {
  success: boolean;
  data: any;
  error?: string;
}

export interface PanelLoadingStates {
  [ComponentType.TimeSeriesPanel]: boolean;
  [ComponentType.OverviewPanel]: boolean;
  [ComponentType.HistogramPanel]: boolean;
  [ComponentType.DataTablePanel]: boolean;
}

export interface PanelErrorStates {
  [ComponentType.TimeSeriesPanel]: string | null;
  [ComponentType.OverviewPanel]: string | null;
  [ComponentType.HistogramPanel]: string | null;
  [ComponentType.DataTablePanel]: string | null;
}

export const useFetchFilteredFileData = () => {
  const [panelLoadingStates, setPanelLoadingStates] = useState<PanelLoadingStates>({
    [ComponentType.TimeSeriesPanel]: false,
    [ComponentType.OverviewPanel]: false,
    [ComponentType.HistogramPanel]: false,
    [ComponentType.DataTablePanel]: false,
  });

  const [panelErrorStates, setPanelErrorStates] = useState<PanelErrorStates>({
    [ComponentType.TimeSeriesPanel]: null,
    [ComponentType.OverviewPanel]: null,
    [ComponentType.HistogramPanel]: null,
    [ComponentType.DataTablePanel]: null,
  });

  // Single API endpoint for all panel data
  const API_ENDPOINT = '/api/v1/data-exploration/panel-data';

  // Fetch data for a single panel
  const fetchPanelData = async (
    panelType: ComponentType,
    filterPayload: FilterPayload,
    isInitialLoad: boolean = false
  ): Promise<PanelDataResponse> => {
    try {
      // Set loading state
      setPanelLoadingStates(prev => ({
        ...prev,
        [panelType]: true
      }));

      // Clear any previous errors
      setPanelErrorStates(prev => ({
        ...prev,
        [panelType]: null
      }));

      console.log(`${isInitialLoad ? 'Initial load' : 'Filter change'} - Calling API for ${panelType}:`, {
        endpoint: `${API_ENDPOINT}?panelType=${panelType}`,
        payload: filterPayload
      });

      // Call the unified API with panel type as query parameter
      // TODO: Replace with real API when backend is ready
      // const response = await postRequest(`${API_ENDPOINT}?panelType=${panelType}`, filterPayload);

      // For now, simulate API call with dummy data
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay

      const dummyResponse = {
        data: {
          success: true,
          data: {
            filteredData: filterPayload, // Echo back filter payload as dummy data
            message: `Dummy filtered data for ${panelType}`,
            panelType,
            timestamp: new Date().toISOString()
          }
        }
      };

      if (dummyResponse.data && dummyResponse.data.success) {
        console.log(`Successfully fetched data for ${panelType}:`, dummyResponse.data.data);
        return {
          success: true,
          data: dummyResponse.data.data
        };
      } else {
        throw new Error('Failed to fetch panel data');
      }
    } catch (error: any) {
      console.error(`Error fetching data for ${panelType}:`, error);
      
      // Set error state
      setPanelErrorStates(prev => ({
        ...prev,
        [panelType]: error.message || 'Failed to fetch data'
      }));

      return {
        success: false,
        data: null,
        error: error.message || 'Failed to fetch data'
      };
    } finally {
      // Clear loading state
      setPanelLoadingStates(prev => ({
        ...prev,
        [panelType]: false
      }));
    }
  };

  // Fetch data for multiple panels
  const fetchMultiplePanelData = async (
    panelTypes: ComponentType[],
    filterPayload: FilterPayload,
    isInitialLoad: boolean = false,
    onPanelDataReceived?: (panelType: ComponentType, data: any) => void
  ): Promise<Record<ComponentType, any>> => {
    console.log(`${isInitialLoad ? 'Initial load' : 'Filter change'} - Fetching data for panels:`, panelTypes);

    const results: Record<ComponentType, any> = {} as Record<ComponentType, any>;

    // Call API for each panel type
    const promises = panelTypes.map(async (panelType) => {
      const result = await fetchPanelData(panelType, filterPayload, isInitialLoad);
      
      if (result.success && result.data) {
        results[panelType] = result.data;
        
        // Call callback if provided (for real-time updates)
        if (onPanelDataReceived) {
          onPanelDataReceived(panelType, result.data);
        }
      }
      
      return { panelType, result };
    });

    // Wait for all API calls to complete
    await Promise.all(promises);

    return results;
  };

  // Create filter payload from current state
  const createFilterPayload = (
    fileId: string,
    fileName: string,
    selectedColumns: any,
    dateFilter: any,
    conditionalFilters: any[],
    annotationFilters: any[],
    operationFilters: any[],
    explorationType: string = 'batch'
  ): FilterPayload => {
    return {
      fileId,
      fileName,
      selectedColumns,
      dateFilter,
      conditionalFilters,
      annotationFilters,
      operationFilters,
      explorationType,
      timestamp: new Date().toISOString()
    };
  };

  return {
    fetchPanelData,
    fetchMultiplePanelData,
    createFilterPayload,
    panelLoadingStates,
    panelErrorStates,
    setPanelLoadingStates,
    setPanelErrorStates
  };
};
