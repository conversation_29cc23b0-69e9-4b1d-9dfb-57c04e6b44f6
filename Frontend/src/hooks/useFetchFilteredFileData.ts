import { useState } from 'react';
import { getRequest } from '../utils/apiHandler';
import { ComponentType } from '../components/Dashboard/Views Section/types';

export interface FilterPayload {
  fileId: string;
  fileName: string;
  selectedColumns: {
    indices: number[];
    headers: string[];
  };
  dateFilter: {
    startDate: string | null;
    endDate: string | null;
  };
  conditionalFilters: any[];
  annotationFilters: any[];
  operationFilters: any[];
}

export interface PanelLoadingStates {
  [ComponentType.TimeSeriesPanel]: boolean;
  [ComponentType.OverviewPanel]: boolean;
  [ComponentType.HistogramPanel]: boolean;
  [ComponentType.DataTablePanel]: boolean;
}

export interface PanelErrorStates {
  [ComponentType.TimeSeriesPanel]: string | null;
  [ComponentType.OverviewPanel]: string | null;
  [ComponentType.HistogramPanel]: string | null;
  [ComponentType.DataTablePanel]: string | null;
}

export const useFetchFilteredFileData = () => {
  const [panelLoadingStates, setPanelLoadingStates] = useState<PanelLoadingStates>({
    [ComponentType.TimeSeriesPanel]: false,
    [ComponentType.OverviewPanel]: false,
    [ComponentType.HistogramPanel]: false,
    [ComponentType.DataTablePanel]: false,
  });

  const [panelErrorStates, setPanelErrorStates] = useState<PanelErrorStates>({
    [ComponentType.TimeSeriesPanel]: null,
    [ComponentType.OverviewPanel]: null,
    [ComponentType.HistogramPanel]: null,
    [ComponentType.DataTablePanel]: null,
  });

  // Simple function to fetch data for all active panels
  const fetchAllPanelData = async (
    panelTypes: ComponentType[],
    filterPayload: FilterPayload,
    onPanelDataReceived: (panelType: ComponentType, data: any) => void
  ) => {
    console.log('@@@ Fetching data for all panels:', panelTypes);
    console.log('@@@ Filter payload:', filterPayload);

    // Call API for each panel type
    panelTypes.forEach(async (panelType) => {
      try {
        console.log(`@@@ Setting loading state for ${panelType}`);
        // Set loading state
        setPanelLoadingStates(prev => ({
          ...prev,
          [panelType]: true
        }));

        // Clear any previous errors
        setPanelErrorStates(prev => ({
          ...prev,
          [panelType]: null
        }));

        // Use existing file endpoint with panel type as query parameter
        const endpoint = `/file/${filterPayload.fileId}?panelType=${panelType}`;

        console.log(`@@@ Calling API for ${panelType}: ${endpoint}`);

        // Call the existing file API with panel type as query parameter
        const response = await getRequest(endpoint);

        console.log(`@@@ API response for ${panelType}:`, response);

        if (response.data && response.data.status === 200) {
          console.log(`@@@ Successfully fetched data for ${panelType}:`, {
            dataType: typeof response.data.data,
            isArray: Array.isArray(response.data.data),
            length: response.data.data?.length,
            firstItem: response.data.data?.[0]
          });
          onPanelDataReceived(panelType, response.data.data);
        } else {
          throw new Error(response.data?.message || 'Failed to fetch panel data');
        }
      } catch (error: any) {
        console.error(`@@@ Error fetching data for ${panelType}:`, error);

        // Set error state
        setPanelErrorStates(prev => ({
          ...prev,
          [panelType]: error.message || 'Failed to fetch data'
        }));
      } finally {
        console.log(`@@@ Clearing loading state for ${panelType}`);
        // Clear loading state
        setPanelLoadingStates(prev => ({
          ...prev,
          [panelType]: false
        }));
      }
    });
  };

  // Create filter payload from current state
  const createFilterPayload = (
    fileId: string,
    fileName: string,
    selectedColumns: any,
    dateFilter: any,
    conditionalFilters: any[],
    annotationFilters: any[],
    operationFilters: any[]
  ): FilterPayload => {
    return {
      fileId,
      fileName,
      selectedColumns,
      dateFilter,
      conditionalFilters,
      annotationFilters,
      operationFilters
    };
  };

  return {
    fetchAllPanelData,
    createFilterPayload,
    panelLoadingStates,
    panelErrorStates
  };
};
